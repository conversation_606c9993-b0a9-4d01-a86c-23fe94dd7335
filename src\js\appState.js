/**
 * ByeVape Central App State
 * Globale Zustandsverwaltung und Koordination aller Module
 */

class AppState {
  constructor() {
    this.logger = window.logger;
    this.errorHandler = null;
    this.stateManager = null;
    this.uiRenderer = null;
    this.trackerLogic = null;
    this.isInitialized = false;
    this.initializationPromise = null;

    this.logger.info('🏗️ AppState constructor called');
  }

  /**
   * App-Zustand initialisieren
   */
  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._performInitialization();
    return this.initializationPromise;
  }

  /**
   * Interne Initialisierung durchführen
   */
  async _performInitialization() {
    const perf = this.logger.startPerformance('AppState initialization');

    try {
      this.logger.info('🔄 Initializing AppState...');

      // Initialize ErrorHandler first
      this.errorHandler = new ErrorHandler(this.logger);
      this.errorHandler.initialize();

      // Initialize StateManager
      this.stateManager = new StateManager();
      await this.stateManager.initialize();

      // Initialize UIRenderer
      this.uiRenderer = new UIRenderer(this.stateManager);
      await this.uiRenderer.initialize();

      // Initialize TrackerLogic
      this.trackerLogic = new TrackerLogic(this.stateManager);
      await this.trackerLogic.initialize();

      // Setup cross-module communication
      this.setupModuleCommunication();

      // Perform initial data checks
      await this.performInitialChecks();

      this.isInitialized = true;
      this.logger.info('✅ AppState initialized successfully');

      // Notify about successful initialization
      this.stateManager.notifyListeners('appInitialized', {
        timestamp: new Date().toISOString()
      });

      return true;

    } catch (error) {
      this.logger.error('❌ Error initializing AppState', error);
      this.handleInitializationError(error);
      return false;
    } finally {
      perf.end();
    }
  }

  /**
   * Module-übergreifende Kommunikation einrichten
   */
  setupModuleCommunication() {
    try {
      // StateManager events
      this.stateManager.addEventListener('stateChanged', (data) => {
        this.handleStateChange(data);
      });

      this.stateManager.addEventListener('saveError', (data) => {
        this.handleSaveError(data);
      });

      this.stateManager.addEventListener('dataRestored', (data) => {
        this.handleDataRestored(data);
      });

      // Setup periodic tasks
      this.setupPeriodicTasks();
      
      console.log('🔗 Module communication setup complete');
      
    } catch (error) {
      console.error('❌ Error setting up module communication:', error);
    }
  }

  /**
   * Periodische Aufgaben einrichten
   */
  setupPeriodicTasks() {
    // Update UI every 30 seconds
    setInterval(() => {
      if (this.isInitialized) {
        this.uiRenderer.renderLastPuffInfo();
      }
    }, 30000);

    // Save data every 5 minutes
    setInterval(() => {
      if (this.isInitialized) {
        this.stateManager.saveUserData();
      }
    }, 5 * 60000);

    // Check for daily reset every hour
    setInterval(() => {
      if (this.isInitialized) {
        this.trackerLogic.checkDailyReset();
      }
    }, 60 * 60000);
  }

  /**
   * Anfängliche Überprüfungen durchführen
   */
  async performInitialChecks() {
    try {
      // Check if daily reset is needed
      this.trackerLogic.checkDailyReset();
      
      // Update daily puff target
      this.trackerLogic.updateDailyPuffTarget();
      
      // Render initial UI
      await this.uiRenderer.renderAll();
      
      console.log('✅ Initial checks completed');
      
    } catch (error) {
      console.error('❌ Error performing initial checks:', error);
    }
  }

  /**
   * Zustandsänderungen behandeln
   */
  handleStateChange(data) {
    try {
      const { path, newValue, oldValue } = data;
      
      // Log significant changes
      if (path.includes('totalPuffsToday') || path.includes('dailyPuffTarget')) {
        console.log(`📊 State change: ${path} = ${newValue} (was: ${oldValue})`);
      }
      
      // Trigger specific actions based on state changes
      if (path === 'userData.totalPuffsToday') {
        this.handlePuffCountChange(newValue, oldValue);
      } else if (path === 'userData.dailyPuffTarget') {
        this.handleTargetChange(newValue, oldValue);
      }
      
    } catch (error) {
      console.error('❌ Error handling state change:', error);
    }
  }

  /**
   * Puff-Anzahl-Änderung behandeln
   */
  handlePuffCountChange(newCount, oldCount) {
    try {
      // Check if we need to show warnings
      const paceCheck = this.trackerLogic.checkConsumptionPace();
      if (paceCheck.status !== 'ok' && paceCheck.status !== 'good') {
        this.uiRenderer.showStatusMessage(paceCheck.message, paceCheck.status);
      }
      
      // Update statistics
      const stats = this.trackerLogic.calculatePuffStatistics();
      if (stats) {
        this.stateManager.updateState('app.currentStats', stats);
      }
      
    } catch (error) {
      console.error('❌ Error handling puff count change:', error);
    }
  }

  /**
   * Ziel-Änderung behandeln
   */
  handleTargetChange(newTarget, oldTarget) {
    try {
      console.log(`🎯 Daily target changed: ${oldTarget} → ${newTarget}`);
      
      // Update UI to reflect new target
      this.uiRenderer.renderProgress();
      
    } catch (error) {
      console.error('❌ Error handling target change:', error);
    }
  }

  /**
   * Speicherfehler behandeln
   */
  handleSaveError(data) {
    try {
      console.error('💾 Save error occurred:', data);
      
      this.uiRenderer.showStatusMessage(
        'Fehler beim Speichern der Daten. Ihre Eingaben könnten verloren gehen.',
        'error'
      );
      
      // Try alternative save methods or notify user
      this.attemptRecovery();
      
    } catch (error) {
      console.error('❌ Error handling save error:', error);
    }
  }

  /**
   * Datenwiederherstellung behandeln
   */
  handleDataRestored(data) {
    try {
      console.log('🔄 Data restored from backup:', data);
      
      this.uiRenderer.showStatusMessage(
        'Daten wurden aus einem Backup wiederhergestellt.',
        'info'
      );
      
      // Refresh UI with restored data
      this.uiRenderer.renderAll();
      
    } catch (error) {
      console.error('❌ Error handling data restoration:', error);
    }
  }

  /**
   * Initialisierungsfehler behandeln
   */
  handleInitializationError(error) {
    try {
      console.error('🚨 Critical initialization error:', error);
      
      // Try to show error to user
      const errorMessage = document.createElement('div');
      errorMessage.className = 'critical-error';
      errorMessage.innerHTML = `
        <h3>Initialisierungsfehler</h3>
        <p>Die App konnte nicht ordnungsgemäß gestartet werden.</p>
        <p>Bitte laden Sie die Seite neu oder kontaktieren Sie den Support.</p>
        <button onclick="location.reload()">Seite neu laden</button>
      `;
      
      document.body.appendChild(errorMessage);
      
    } catch (displayError) {
      console.error('❌ Could not display initialization error:', displayError);
    }
  }

  /**
   * Wiederherstellungsversuch
   */
  async attemptRecovery() {
    try {
      console.log('🔄 Attempting data recovery...');
      
      // Try to restore from backup
      if (this.stateManager.restoreFromBackup()) {
        this.uiRenderer.showStatusMessage('Daten erfolgreich wiederhergestellt.', 'success');
        return true;
      }
      
      // If backup restoration fails, try to save current state again
      const saveSuccess = await this.stateManager.saveUserData();
      if (saveSuccess) {
        this.uiRenderer.showStatusMessage('Daten erfolgreich gespeichert.', 'success');
        return true;
      }
      
      // Last resort: export data for user
      this.stateManager.exportUserData();
      this.uiRenderer.showStatusMessage(
        'Automatischer Download Ihrer Daten gestartet. Bitte sichern Sie diese Datei.',
        'warning'
      );
      
      return false;
      
    } catch (error) {
      console.error('❌ Error during recovery attempt:', error);
      return false;
    }
  }

  // ===================================
  // PUBLIC API METHODS
  // ===================================

  /**
   * Puffs protokollieren
   */
  async logPuffs(count) {
    if (!this.isInitialized) {
      this.logger.error('❌ AppState not initialized');
      return false;
    }

    return await this.errorHandler.retry(async () => {
      this.logger.userAction('logPuffs', { count });

      // Validate input
      const validation = this.validatePuffInput(count);
      if (!validation.isValid) {
        this.uiRenderer.showStatusMessage(validation.error, 'warning');
        throw new Error(`Invalid input: ${validation.error}`);
      }

      // Log puffs
      const userData = this.stateManager.getUserData();
      const newTotal = userData.totalPuffsToday + validation.value;

      // Update state
      this.stateManager.updateState('userData.totalPuffsToday', newTotal);
      this.stateManager.updateState('userData.lastPuffTime', new Date().toISOString());

      // Add to puff times array
      const puffTimes = [...userData.puffTimes];
      for (let i = 0; i < validation.value; i++) {
        puffTimes.push(new Date().toISOString());
      }
      this.stateManager.updateState('userData.puffTimes', puffTimes);

      // Save data
      await this.stateManager.saveUserData();

      this.logger.info(`📝 Logged ${validation.value} puff(s). Total: ${newTotal}`);
      return true;

    }, { operation: 'logPuffs', count }).then(result => {
      if (!result.success) {
        this.logger.error('❌ Error logging puffs', result.error);
        this.uiRenderer.showStatusMessage('Fehler beim Protokollieren der Puffs.', 'error');
        return false;
      }
      return result.result;
    });
  }

  /**
   * Gerätesynchronisation
   */
  async syncDevicePuffs(deviceValue) {
    if (!this.isInitialized) {
      this.logger.error('❌ AppState not initialized');
      return false;
    }

    return await this.errorHandler.retry(async () => {
      this.logger.userAction('syncDevicePuffs', { deviceValue });

      // Convert to number and validate
      const numericValue = parseInt(deviceValue);
      if (isNaN(numericValue) || numericValue < 0 || numericValue > 9999) {
        const message = 'Bitte geben Sie eine gültige Zahl zwischen 0 und 9999 ein.';
        this.uiRenderer.showStatusMessage(message, 'warning');
        throw new Error(message);
      }

      const userData = this.stateManager.getUserData();
      const lastValue = userData.lastRecordedDevicePuffValue || 0;

      if (numericValue <= lastValue) {
        const message = `Gerätewert muss höher als ${lastValue} sein.`;
        this.uiRenderer.showStatusMessage(message, 'warning');
        throw new Error(message);
      }

      const puffDelta = numericValue - lastValue;

      // Update device value
      this.stateManager.updateState('userData.lastRecordedDevicePuffValue', numericValue);

      // Log the delta puffs
      const result = await this.logPuffs(puffDelta);

      this.logger.info(`📱 Device synced: ${puffDelta} new puffs (device total: ${numericValue})`);
      return result;

    }, { operation: 'syncDevicePuffs', deviceValue }).then(result => {
      if (!result.success) {
        this.logger.error('❌ Error syncing device puffs', result.error);
        this.uiRenderer.showStatusMessage('Fehler bei der Gerätesynchronisation.', 'error');
        return false;
      }
      return result.result;
    });
  }

  /**
   * Letzten Puff rückgängig machen
   */
  async undoLastPuff() {
    if (!this.isInitialized) {
      this.logger.error('❌ AppState not initialized');
      return false;
    }

    return await this.errorHandler.retry(async () => {
      this.logger.userAction('undoLastPuff');

      const userData = this.stateManager.getUserData();

      if (userData.totalPuffsToday <= 0) {
        const message = 'Keine Puffs zum Rückgängigmachen vorhanden.';
        this.uiRenderer.showStatusMessage(message, 'info');
        throw new Error(message);
      }

      // Update counts
      this.stateManager.updateState('userData.totalPuffsToday', userData.totalPuffsToday - 1);

      // Remove last puff time
      const puffTimes = [...userData.puffTimes];
      puffTimes.pop();
      this.stateManager.updateState('userData.puffTimes', puffTimes);

      // Update last puff time
      const lastPuffTime = puffTimes.length > 0 ? puffTimes[puffTimes.length - 1] : null;
      this.stateManager.updateState('userData.lastPuffTime', lastPuffTime);

      // Save data
      await this.stateManager.saveUserData();

      this.uiRenderer.showStatusMessage('Letzter Puff wurde rückgängig gemacht.', 'success');
      this.logger.info('↩️ Last puff undone');
      return true;

    }, { operation: 'undoLastPuff' }).then(result => {
      if (!result.success) {
        this.logger.error('❌ Error undoing last puff', result.error);
        this.uiRenderer.showStatusMessage('Fehler beim Rückgängigmachen.', 'error');
        return false;
      }
      return result.result;
    });
  }

  /**
   * Eingabe validieren
   */
  validatePuffInput(input) {
    try {
      const numValue = parseInt(input);

      if (isNaN(numValue)) {
        return { isValid: false, error: 'Please enter a valid number.' };
      }

      if (numValue < 1) {
        return { isValid: false, error: 'At least 1 puff must be entered.' };
      }

      if (numValue > 9999) {
        return { isValid: false, error: 'Maximum 9999 puffs can be entered at once.' };
      }

      return { isValid: true, value: numValue };

    } catch (error) {
      return { isValid: false, error: 'Error validating input.' };
    }
  }

  /**
   * App-Status abrufen
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasStateManager: !!this.stateManager,
      hasUIRenderer: !!this.uiRenderer,
      hasTrackerLogic: !!this.trackerLogic
    };
  }

  /**
   * Statistiken abrufen
   */
  getStatistics() {
    if (!this.isInitialized || !this.trackerLogic) {
      return null;
    }

    return this.trackerLogic.calculatePuffStatistics();
  }

  /**
   * Debug-Informationen abrufen
   */
  getDebugInfo() {
    return {
      appState: this.getStatus(),
      logger: this.logger.getDebugInfo(),
      errorHandler: this.errorHandler ? this.errorHandler.getErrorStats() : null,
      stateManager: this.stateManager ? {
        isInitialized: this.stateManager.isInitialized,
        userDataKeys: Object.keys(this.stateManager.getUserData()),
        historySize: Object.keys(this.stateManager.getDailyHistory()).length
      } : null,
      performance: {
        memory: performance.memory ? {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
        } : 'not available',
        timing: performance.timing ? {
          loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart + ' ms',
          domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart + ' ms'
        } : 'not available'
      }
    };
  }

  /**
   * Debug-Panel anzeigen/ausblenden
   */
  toggleDebugPanel() {
    if (!this.logger.shouldLog('debug')) {
      this.logger.warn('Debug panel only available in debug mode');
      return;
    }

    let panel = document.getElementById('debug-panel');

    if (!panel) {
      panel = this.createDebugPanel();
      document.body.appendChild(panel);
    }

    panel.classList.toggle('visible');

    if (panel.classList.contains('visible')) {
      this.updateDebugPanel();
      // Update every 5 seconds while visible
      this.debugInterval = setInterval(() => {
        this.updateDebugPanel();
      }, 5000);
    } else {
      if (this.debugInterval) {
        clearInterval(this.debugInterval);
        this.debugInterval = null;
      }
    }
  }

  /**
   * Debug-Panel erstellen
   */
  createDebugPanel() {
    const panel = document.createElement('div');
    panel.id = 'debug-panel';
    panel.className = 'debug-panel';
    panel.innerHTML = `
      <h4>Debug Info</h4>
      <div id="debug-content"></div>
      <button onclick="appState.exportDebugInfo()" style="margin-top: 10px; padding: 5px 10px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">
        Export Debug Info
      </button>
      <button onclick="appState.toggleDebugPanel()" style="margin-top: 5px; padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 3px; cursor: pointer;">
        Close
      </button>
    `;
    return panel;
  }

  /**
   * Debug-Panel aktualisieren
   */
  updateDebugPanel() {
    const content = document.getElementById('debug-content');
    if (!content) return;

    const debugInfo = this.getDebugInfo();

    content.innerHTML = `
      <div class="debug-item">
        <span class="debug-label">Status:</span>
        <span class="debug-value">${debugInfo.appState.isInitialized ? 'Initialized' : 'Not Initialized'}</span>
      </div>
      <div class="debug-item">
        <span class="debug-label">Log Level:</span>
        <span class="debug-value">${debugInfo.logger.logLevel}</span>
      </div>
      <div class="debug-item">
        <span class="debug-label">Errors:</span>
        <span class="debug-value">${debugInfo.errorHandler ? debugInfo.errorHandler.totalErrors : 0}</span>
      </div>
      <div class="debug-item">
        <span class="debug-label">Memory:</span>
        <span class="debug-value">${debugInfo.performance.memory.used || 'N/A'}</span>
      </div>
      <div class="debug-item">
        <span class="debug-label">Storage:</span>
        <span class="debug-value">${debugInfo.logger.localStorage.usage}</span>
      </div>
    `;
  }

  /**
   * Debug-Informationen exportieren
   */
  exportDebugInfo() {
    try {
      const debugInfo = this.getDebugInfo();
      const dataStr = JSON.stringify(debugInfo, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `byevape-debug-${new Date().toISOString().split('T')[0]}.json`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);

      this.logger.info('Debug info exported successfully');

    } catch (error) {
      this.logger.error('Failed to export debug info', error);
    }
  }
}

// Globale App-Instanz erstellen
const appState = new AppState();

// Export für ES6 Module
export default appState;

// Globale Verfügbarkeit
window.appState = appState;
