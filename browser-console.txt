📝 Logger initialized with level: warn index-BkK3wXLR.js:44:13
ℹ️ Running in browser mode - SplashScreen not available index-BkK3wXLR.js:2606:13
🔍 Detecting environment... 
Object { hasCapacitor: false, hasCapacitorPlugins: false, readyState: "interactive", userAgent: "Browser" }
index-BkK3wXLR.js:5104:9
🌐 Running in web browser index-BkK3wXLR.js:5126:11
🚀 Initializing ByeVape app with modular architecture... index-BkK3wXLR.js:5080:13
🔄 Showing splash screen... index-BkK3wXLR.js:4993:13
ℹ️ Running in browser - no splash screen to show index-BkK3wXLR.js:5001:15
🚀 ByeVape main entry point loaded index-BkK3wXLR.js:5133:9
Folgende nicht unterstützte entryTypes werden ignoriert: longtask. index-BkK3wXLR.js:399:18
Keine gültigen entryTypes; Registrierung wird abgebrochen. index-BkK3wXLR.js:399:18
🔧 StateManager initialized index-BkK3wXLR.js:662:13
🔄 Initializing StateManager... index-BkK3wXLR.js:735:15
✅ StateManager initialized successfully index-BkK3wXLR.js:745:15
🎨 UIRenderer initialized index-BkK3wXLR.js:1296:13
🔄 Initializing UIRenderer... index-BkK3wXLR.js:1303:15
⚠️ Element not found: current-day index-BkK3wXLR.js:1338:17
⚠️ Element not found: days-remaining index-BkK3wXLR.js:1338:17
⚠️ Element not found: progress-percentage index-BkK3wXLR.js:1338:17
📦 Cached 9 DOM elements index-BkK3wXLR.js:1341:13
🎨 Rendering all UI elements... index-BkK3wXLR.js:1394:15
🔄 Progress updated: 0.0% (0/0) 2 index-BkK3wXLR.js:1486:15
✅ All UI elements rendered index-BkK3wXLR.js:1401:15
✅ UIRenderer initialized successfully index-BkK3wXLR.js:1308:15
📊 TrackerLogic initialized index-BkK3wXLR.js:1699:13
🔄 Initializing TrackerLogic... index-BkK3wXLR.js:1706:15
🔄 Progress updated: 0.0% (0/0) index-BkK3wXLR.js:1486:15
🔄 State updated: userData.dailyPuffTarget = 0 index-BkK3wXLR.js:910:15
🎯 Daily puff target updated: 0 puffs index-BkK3wXLR.js:1744:15
✅ TrackerLogic initialized successfully index-BkK3wXLR.js:1710:15
🔗 Module communication setup complete index-BkK3wXLR.js:2101:15
🔄 Performing daily reset... index-BkK3wXLR.js:1930:15
🔄 Progress updated: 0.0% (0/0) index-BkK3wXLR.js:1486:15
📊 State change: userData.totalPuffsToday = 0 (was: 0) index-BkK3wXLR.js:2146:17
⏰ Puff pace check: 5.0h elapsed, expected: 0.0, actual: 0 index-BkK3wXLR.js:1775:15
📊 Puff rate: 0.00/h (target: 0.00/h), usage: 0.0% index-BkK3wXLR.js:1776:15
🔄 State updated: ui.statusMessage = 🎉 Ausgezeichnet! Sie haben heute 5.0 Stunden ohne Puffs verbracht. index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusType = great index-BkK3wXLR.js:910:15
🔄 State updated: ui.showStatusMessage = true index-BkK3wXLR.js:910:15
🔄 State updated: app.currentStats = [object Object] index-BkK3wXLR.js:910:15
🔄 State updated: userData.totalPuffsToday = 0 index-BkK3wXLR.js:910:15
🔄 State updated: userData.puffTimes = index-BkK3wXLR.js:910:15
🔄 State updated: userData.lastPuffTime = null index-BkK3wXLR.js:910:15
🔄 State updated: userData.lastResetDate = 2025-06-26 index-BkK3wXLR.js:910:15
🔄 Progress updated: 0.0% (0/0) index-BkK3wXLR.js:1486:15
📊 State change: userData.dailyPuffTarget = 0 (was: 0) index-BkK3wXLR.js:2146:17
🎯 Daily target changed: 0 → 0 index-BkK3wXLR.js:2179:15
🔄 Progress updated: 0.0% (0/0) index-BkK3wXLR.js:1486:15
🔄 State updated: userData.dailyPuffTarget = 0 index-BkK3wXLR.js:910:15
🎯 Daily puff target updated: 0 puffs index-BkK3wXLR.js:1744:15
🔄 State updated: userData.currentDay = 2 index-BkK3wXLR.js:910:15
📦 Enhanced data backup created index-BkK3wXLR.js:1067:15
✅ User data saved to localStorage index-BkK3wXLR.js:936:17
🔄 Progress updated: 0.0% (0/0) index-BkK3wXLR.js:1486:15
✅ Daily reset completed for day 2 index-BkK3wXLR.js:1939:15
🔄 Progress updated: 0.0% (0/0) index-BkK3wXLR.js:1486:15
📊 State change: userData.dailyPuffTarget = 0 (was: 0) index-BkK3wXLR.js:2146:17
🎯 Daily target changed: 0 → 0 index-BkK3wXLR.js:2179:15
🔄 Progress updated: 0.0% (0/0) index-BkK3wXLR.js:1486:15
🔄 State updated: userData.dailyPuffTarget = 0 index-BkK3wXLR.js:910:15
🎯 Daily puff target updated: 0 puffs index-BkK3wXLR.js:1744:15
🎨 Rendering all UI elements... index-BkK3wXLR.js:1394:15
🔄 Progress updated: 0.0% (0/0) 2 index-BkK3wXLR.js:1486:15
✅ All UI elements rendered index-BkK3wXLR.js:1401:15
✅ Initial checks completed index-BkK3wXLR.js:2134:15
✅ ByeVape app initialized successfully with modular architecture index-BkK3wXLR.js:5084:15
❌ Critical error during modular initialization: ReferenceError: ByeVapeApp is not defined
    initializeAppWithModules http://192.168.2.108:3001/assets/index-BkK3wXLR.js:5085
    async* http://192.168.2.108:3001/assets/index-BkK3wXLR.js:5130
index-BkK3wXLR.js:5099:13
🔄 Falling back to legacy initialization... index-BkK3wXLR.js:5100:13
Initializing ByeVape AppState... index-BkK3wXLR.js:2666:15
User data loaded successfully index-BkK3wXLR.js:2685:17
No daily history found index-BkK3wXLR.js:2694:17
🚀 Starting app - isFirstLaunch: true, startDate: null index-BkK3wXLR.js:3463:13
👋 First launch detected - showing onboarding index-BkK3wXLR.js:3465:15
🔄 showPage called with pageId: onboarding index-BkK3wXLR.js:3496:13
📄 Found 4 pages to hide index-BkK3wXLR.js:3498:13
   Hiding page 0: onboarding-page index-BkK3wXLR.js:3500:15
   Hiding page 1: tracker-page index-BkK3wXLR.js:3500:15
   Hiding page 2: statistics-page index-BkK3wXLR.js:3500:15
   Hiding page 3: settings-page index-BkK3wXLR.js:3500:15
🎯 Target page element: 
<div id="onboarding-page" class="page" style="display: none;">
index-BkK3wXLR.js:3504:13
✅ Showing page: onboarding-page index-BkK3wXLR.js:3507:15
📊 Page styles - display: block, visibility: visible, opacity: 1 index-BkK3wXLR.js:3509:15
🧭 Bottom navigation element: 
<nav id="bottom-nav" class="bottom-navigation">
index-BkK3wXLR.js:3519:13
🔒 Navigation hidden for onboarding index-BkK3wXLR.js:3523:17
✨ showPage completed for: onboarding index-BkK3wXLR.js:3531:13
ByeVape AppState initialized successfully index-BkK3wXLR.js:2671:15
ByeVape app initialized successfully index-BkK3wXLR.js:5041:13
✅ Global functions attached to window index-BkK3wXLR.js:5071:13
🔄 Hiding splash screen... index-BkK3wXLR.js:5009:13
ℹ️ Running in browser - no splash screen to hide index-BkK3wXLR.js:5014:15
🔄 State updated: ui.showStatusMessage = false index-BkK3wXLR.js:910:15
