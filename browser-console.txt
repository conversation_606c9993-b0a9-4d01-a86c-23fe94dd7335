
📝 Logger initialized with level: debug index-BkK3wXLR.js:44:13
🔄 State updated: ui.statusMessage = index-BkK3wXLR.js:910:15
🔄 Global quickAddPuffs called with count: 1 index-BkK3wXLR.js:4773:11
📦 Data backup created index-BkK3wXLR.js:2841:15
✅ User data saved to localStorage index-BkK3wXLR.js:2787:15
✅ Daily history saved to localStorage index-BkK3wXLR.js:2800:15
📊 Daily stats recorded for 2025-06-26 - Puffs: 47/435 index-BkK3wXLR.js:2907:13
📝 Logged 1 puff(s). Total today: 47/435 index-BkK3wXLR.js:3265:15
⏰ Puff pace check: 5.7h elapsed, expected: 137.2, actual: 47 index-BkK3wXLR.js:2981:13
📊 Puff rate: 8.28/h (target: 24.17/h), usage: 10.8% index-BkK3wXLR.js:2982:13
✅ Quick added 1 puff(s) index-BkK3wXLR.js:4785:15
🔄 Updating tracker data... index-BkK3wXLR.js:3712:13
📉 Showing reduction tracking interface index-BkK3wXLR.js:3732:15
📉 Updating reduction tracker... index-BkK3wXLR.js:3759:13
⏰ Puff pace check: 5.7h elapsed, expected: 137.2, actual: 47 index-BkK3wXLR.js:2981:13
📊 Puff rate: 8.28/h (target: 24.17/h), usage: 10.8% index-BkK3wXLR.js:2982:13
🔄 Progress updated: 10.8% (47/435) index-BkK3wXLR.js:3826:13
⏰ Puff pace check: 5.7h elapsed, expected: 137.2, actual: 47 index-BkK3wXLR.js:2981:13
📊 Puff rate: 8.28/h (target: 24.17/h), usage: 10.8% index-BkK3wXLR.js:2982:13
📊 Puffs: 47/435 index-BkK3wXLR.js:3774:13
✅ Tracker updated - Day 1 index-BkK3wXLR.js:3724:13
🔍 [DEBUG] 🔄 Global undoLastPuff called 
Object {  }
index-BkK3wXLR.js:111:13
🔍 [DEBUG] Retry attempt 1/3 
Object { operation: "undoLastPuff" }
index-BkK3wXLR.js:111:13
ℹ️ [INFO] User action: undoLastPuff 
Object { action: "undoLastPuff", details: {}, timestamp: 1750930832505 }
index-BkK3wXLR.js:120:13
🔄 Progress updated: 1.6% (7/435) index-BkK3wXLR.js:1486:15
📊 State change: userData.totalPuffsToday = 7 (was: 8) index-BkK3wXLR.js:2146:17
⏰ Puff pace check: 5.7h elapsed, expected: 137.2, actual: 7 index-BkK3wXLR.js:1775:15
📊 Puff rate: 1.23/h (target: 24.17/h), usage: 1.6% index-BkK3wXLR.js:1776:15
🔄 State updated: ui.statusMessage = 🌟 Großartiges Puff-Tempo! Sie machen es besser als erwartet. index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusType = great index-BkK3wXLR.js:910:15
🔄 State updated: ui.showStatusMessage = true index-BkK3wXLR.js:910:15
🔄 State updated: app.currentStats = [object Object] index-BkK3wXLR.js:910:15
🔄 State updated: userData.totalPuffsToday = 7 index-BkK3wXLR.js:910:15
🔄 State updated: userData.puffTimes = 2025-06-26T09:35:28.300Z,2025-06-26T09:35:29.576Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:41.521Z index-BkK3wXLR.js:910:15
🔄 State updated: userData.lastPuffTime = 2025-06-26T09:35:41.521Z index-BkK3wXLR.js:910:15
📦 Enhanced data backup created index-BkK3wXLR.js:1067:15
✅ User data saved to localStorage index-BkK3wXLR.js:936:17
🔄 Progress updated: 1.6% (7/435) index-BkK3wXLR.js:1486:15
🔄 State updated: ui.statusMessage = Letzter Puff wurde rückgängig gemacht. index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusType = success index-BkK3wXLR.js:910:15
🔄 State updated: ui.showStatusMessage = true index-BkK3wXLR.js:910:15
ℹ️ [INFO] ↩️ Last puff undone 
Object {  }
index-BkK3wXLR.js:120:13
ℹ️ [INFO] ✅ Last puff undone 
Object {  }
index-BkK3wXLR.js:120:13
🔄 State updated: ui.showStatusMessage = false index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusMessage = index-BkK3wXLR.js:910:15
🔍 [DEBUG] 🔄 Global logPuffs called 
Object {  }
index-BkK3wXLR.js:111:13
🔍 [DEBUG] Retry attempt 1/3 
Object { operation: "logPuffs", count: "6" }
index-BkK3wXLR.js:111:13
ℹ️ [INFO] User action: logPuffs 
Object { action: "logPuffs", details: {…}, timestamp: 1750930834717 }
index-BkK3wXLR.js:120:13
🔄 Progress updated: 3.0% (13/435) index-BkK3wXLR.js:1486:15
📊 State change: userData.totalPuffsToday = 13 (was: 7) index-BkK3wXLR.js:2146:17
⏰ Puff pace check: 5.7h elapsed, expected: 137.2, actual: 13 index-BkK3wXLR.js:1775:15
📊 Puff rate: 2.29/h (target: 24.17/h), usage: 3.0% index-BkK3wXLR.js:1776:15
🔄 State updated: ui.statusMessage = 🌟 Großartiges Puff-Tempo! Sie machen es besser als erwartet. index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusType = great index-BkK3wXLR.js:910:15
🔄 State updated: ui.showStatusMessage = true index-BkK3wXLR.js:910:15
🔄 State updated: app.currentStats = [object Object] index-BkK3wXLR.js:910:15
🔄 State updated: userData.totalPuffsToday = 13 index-BkK3wXLR.js:910:15
🔄 State updated: userData.lastPuffTime = 2025-06-26T09:40:34.718Z index-BkK3wXLR.js:910:15
🔄 State updated: userData.puffTimes = 2025-06-26T09:35:28.300Z,2025-06-26T09:35:29.576Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:41.521Z,2025-06-26T09:40:34.718Z,2025-06-26T09:40:34.718Z,2025-06-26T09:40:34.718Z,2025-06-26T09:40:34.718Z,2025-06-26T09:40:34.718Z,2025-06-26T09:40:34.718Z index-BkK3wXLR.js:910:15
📦 Enhanced data backup created index-BkK3wXLR.js:1067:15
✅ User data saved to localStorage index-BkK3wXLR.js:936:17
🔄 Progress updated: 3.0% (13/435) index-BkK3wXLR.js:1486:15
ℹ️ [INFO] 📝 Logged 6 puff(s). Total: 13 
Object {  }
index-BkK3wXLR.js:120:13
🔍 [DEBUG] 🔄 Global undoLastPuff called 
Object {  }
index-BkK3wXLR.js:111:13
🔍 [DEBUG] Retry attempt 1/3 
Object { operation: "undoLastPuff" }
index-BkK3wXLR.js:111:13
ℹ️ [INFO] User action: undoLastPuff 
Object { action: "undoLastPuff", details: {}, timestamp: 1750930836363 }
index-BkK3wXLR.js:120:13
🔄 Progress updated: 2.8% (12/435) index-BkK3wXLR.js:1486:15
📊 State change: userData.totalPuffsToday = 12 (was: 13) index-BkK3wXLR.js:2146:17
⏰ Puff pace check: 5.7h elapsed, expected: 137.2, actual: 12 index-BkK3wXLR.js:1775:15
📊 Puff rate: 2.11/h (target: 24.17/h), usage: 2.8% index-BkK3wXLR.js:1776:15
🔄 State updated: ui.statusMessage = 🌟 Großartiges Puff-Tempo! Sie machen es besser als erwartet. index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusType = great index-BkK3wXLR.js:910:15
🔄 State updated: ui.showStatusMessage = true index-BkK3wXLR.js:910:15
🔄 State updated: app.currentStats = [object Object] index-BkK3wXLR.js:910:15
🔄 State updated: userData.totalPuffsToday = 12 index-BkK3wXLR.js:910:15
🔄 State updated: userData.puffTimes = 2025-06-26T09:35:28.300Z,2025-06-26T09:35:29.576Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:37.453Z,2025-06-26T09:35:41.521Z,2025-06-26T09:40:34.718Z,2025-06-26T09:40:34.718Z,2025-06-26T09:40:34.718Z,2025-06-26T09:40:34.718Z,2025-06-26T09:40:34.718Z index-BkK3wXLR.js:910:15
🔄 State updated: userData.lastPuffTime = 2025-06-26T09:40:34.718Z index-BkK3wXLR.js:910:15
📦 Enhanced data backup created index-BkK3wXLR.js:1067:15
✅ User data saved to localStorage index-BkK3wXLR.js:936:17
🔄 Progress updated: 2.8% (12/435) index-BkK3wXLR.js:1486:15
🔄 State updated: ui.statusMessage = Letzter Puff wurde rückgängig gemacht. index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusType = success index-BkK3wXLR.js:910:15
🔄 State updated: ui.showStatusMessage = true index-BkK3wXLR.js:910:15
ℹ️ [INFO] ↩️ Last puff undone 
Object {  }
index-BkK3wXLR.js:120:13
ℹ️ [INFO] ✅ Last puff undone 
Object {  }
index-BkK3wXLR.js:120:13
🔄 State updated: ui.showStatusMessage = false index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusMessage = index-BkK3wXLR.js:910:15
🔄 State updated: ui.showStatusMessage = false index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusMessage = index-BkK3wXLR.js:910:15
🔄 State updated: ui.showStatusMessage = false index-BkK3wXLR.js:910:15
🔄 State updated: ui.statusMessage = index-BkK3wXLR.js:910:15

​

